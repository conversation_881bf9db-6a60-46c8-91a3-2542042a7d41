import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/story.dart';
import '../models/story_category.dart';
import '../models/chat_session.dart';
import '../ui/design_spec.dart';
import '../utils/story_chat_adapter.dart';
import '../services/chat_session_manager.dart';
import '../services/user_profile_service.dart';
import '../services/user_interaction_service.dart';
import 'chat_page.dart';

class StoryDetailPage extends StatefulWidget {
  final Story story;
  final List<StoryCategory> categories;

  const StoryDetailPage({
    super.key,
    required this.story,
    required this.categories,
  });

  @override
  State<StoryDetailPage> createState() => _StoryDetailPageState();
}

class _StoryDetailPageState extends State<StoryDetailPage> {
  bool _isFavorited = false;
  bool _isLiked = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserInteractions();
  }

  void _loadUserInteractions() async {
    try {
      final service = await UserInteractionService.getInstance();
      setState(() {
        _isFavorited = service.isStoryFavorited(widget.story.id);
        _isLiked = service.isStoryLiked(widget.story.id);
      });
    } catch (e) {
      // 如果加载失败，使用默认状态
      setState(() {
        _isFavorited = false;
        _isLiked = false;
      });
    }
  }

  String _getLocalizedString(Map<String, String> localizedMap, String locale) {
    return localizedMap[locale] ?? localizedMap['zh'] ?? localizedMap['en'] ?? '';
  }

  StoryCategory? _getStoryCategory() {
    return widget.categories.firstWhere(
      (category) => category.id == widget.story.categoryId,
      orElse: () => widget.categories.first,
    );
  }

  void _toggleFavorite() async {
    try {
      final service = await UserInteractionService.getInstance();
      final localizations = AppLocalizations.of(context);
      final locale = localizations?.localeName ?? 'zh';

      if (_isFavorited) {
        await service.unfavoriteStory(widget.story.id);
      } else {
        await service.favoriteStory(widget.story, locale);
      }

      setState(() {
        _isFavorited = !_isFavorited;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations?.operationSuccess ?? '操作成功'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.operationFailed ?? '操作失败'}: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleLike() async {
    try {
      final service = await UserInteractionService.getInstance();
      final localizations = AppLocalizations.of(context);
      final locale = localizations?.localeName ?? 'zh';

      if (_isLiked) {
        await service.unlikeStory(widget.story.id);
      } else {
        await service.likeStory(widget.story, locale);
      }

      setState(() {
        _isLiked = !_isLiked;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations?.operationSuccess ?? '操作成功'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.operationFailed ?? '操作失败'}: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startStory() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final localizations = AppLocalizations.of(context);
      final locale = localizations?.localeName ?? 'zh';

      // 检查是否有保存的会话
      final existingSession = await ChatSessionManager.instance.getLatestSessionForStory(widget.story.id);

      ChatSession chatSession;
      if (existingSession != null) {
        // 使用已保存的会话
        chatSession = existingSession;
      } else {
        // 创建新会话
        chatSession = StoryChatAdapter.createChatSessionFromStory(widget.story, locale);
      }

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatPage(session: chatSession),
          ),
        );
      }
    } catch (e) {
      // 处理错误
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).startStoryFailed(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final locale = localizations.localeName;
    final category = _getStoryCategory();
    
    final storyTitle = _getLocalizedString(widget.story.title, locale);
    final backgroundSetting = _getLocalizedString(widget.story.backgroundSetting, locale);
    final characterSetting = _getLocalizedString(widget.story.characterSetting, locale);

    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      body: CustomScrollView(
        slivers: [
          // 故事封面图片和返回按钮
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: DesignSpec.secondaryBackground,
            leading: Container(
              margin: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: const BorderRadius.all(Radius.circular(50)),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // 故事封面图片
                  Image.asset(
                    widget.story.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image_not_supported,
                          size: 64,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                  // 渐变遮罩
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 故事详情内容
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 故事标题
                  Text(
                    storyTitle,
                    style: const TextStyle(
                      fontSize: DesignSpec.fontSize3Xl,
                      fontWeight: DesignSpec.fontWeightBold,
                      color: DesignSpec.primaryText,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // 故事分类和时长
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: category?.color ?? Colors.grey,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '${localizations.category}: ${category?.name ?? ''}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: DesignSpec.fontSizeSm,
                            fontWeight: DesignSpec.fontWeightMedium,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // 交互按钮区域
                  Row(
                    children: [
                      _buildActionButton(
                        icon: _isFavorited ? Icons.favorite : Icons.favorite_border,
                        label: localizations.favorite,
                        isActive: _isFavorited,
                        onTap: _toggleFavorite,
                      ),
                      const SizedBox(width: 20),
                      _buildActionButton(
                        icon: _isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                        label: localizations.like,
                        isActive: _isLiked,
                        onTap: _toggleLike,
                      ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  
                  // 故事人物
                  _buildSectionTitle(localizations.storyCharacters),
                  const SizedBox(height: 16),
                  _buildCharacterSection(characterSetting),
                  const SizedBox(height: 30),
                  
                  // 故事详情内容
                  _buildSectionTitle(localizations.storyDescription),
                  const SizedBox(height: 16),
                  _buildStoryContent(backgroundSetting),
                  const SizedBox(height: 40),
                  
                  // 开始按钮
                  _buildStartButton(localizations),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isActive ? DesignSpec.primaryItemSelected : DesignSpec.secondaryBackground,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isActive ? DesignSpec.primaryItemSelected : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? Colors.white : Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[600],
                fontSize: DesignSpec.fontSizeSm,
                fontWeight: DesignSpec.fontWeightMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: DesignSpec.fontSizeXl,
        fontWeight: DesignSpec.fontWeightBold,
        color: DesignSpec.primaryText,
      ),
    );
  }

  Widget _buildCharacterSection(String characterSetting) {
    return FutureBuilder(
      future: UserProfileService.getInstance().then((service) => service.getCurrentProfile()),
      builder: (context, snapshot) {
        final userProfile = snapshot.data;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: DesignSpec.secondaryBackground,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 故事人物设定
              Text(
                characterSetting,
                style: TextStyle(
                  fontSize: DesignSpec.fontSizeBase,
                  color: Colors.grey[700],
                  height: 1.5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStoryContent(String content) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        content,
        style: TextStyle(
          fontSize: DesignSpec.fontSizeBase,
          color: Colors.grey[700],
          height: 1.6,
        ),
      ),
    );
  }

  Widget _buildStartButton(AppLocalizations localizations) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _startStory,
        style: ElevatedButton.styleFrom(
          backgroundColor: DesignSpec.primaryItemSelected,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.play_arrow, size: 24, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    localizations.startStory,
                    style: const TextStyle(
                      fontSize: DesignSpec.fontSizeLg,
                      fontWeight: DesignSpec.fontWeightSemiBold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
