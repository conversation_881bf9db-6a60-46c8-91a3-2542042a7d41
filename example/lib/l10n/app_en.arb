{"@@locale": "en", "appTitle": "DreamTales", "storyBoard": "StoryBoard", "chat": "History", "profile": "Profile", "searchHint": "Search for a story", "noSearchResults": "No stories found", "storyCategories": "Story Categories", "categoryStories": "Category Stories", "mostPopular": "Most Popular", "startConversation": "Start conversation!", "inputMessage": "Enter message...", "send": "Send", "renameSession": "Rename Session", "deleteSession": "Delete Session", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete this session? This action cannot be undone.", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "loadFailed": "Loading failed, please try again later", "retry": "Retry", "suspense": "Suspense", "sciFi": "Sci-Fi", "fantasy": "Fantasy", "nickname": "Nickname", "avatar": "Avatar", "editNickname": "Edit Nickname", "editAvatar": "Edit Avatar", "languageSettings": "Language Settings", "privacyPolicy": "Privacy Policy", "termsOfUse": "Terms of Use", "appVersion": "App Version", "save": "Save", "selectAvatar": "Select Avatar", "enterNickname": "Enter your nickname", "nicknameCannotBeEmpty": "Nickname cannot be empty", "profileSaved": "Profile saved successfully", "profileSaveFailed": "Failed to save profile", "gender": "Gender", "male": "Male", "female": "Female", "unspecified": "Unspecified", "maleAvatars": "Male", "femaleAvatars": "Female", "history": "History", "storyHijrahTitle": "The Story of <PERSON><PERSON><PERSON>", "storyHijrahDesc": "The migration of <PERSON> from Mecca to Medina", "storyBadrTitle": "The Battle of Badr", "storyBadrDesc": "The first major battle in Islam between Medina and Mecca", "storyEarlyDaysTitle": "Early Days", "storyEarlyDaysDesc": "The early days of Islam and the first revelations", "storyMusaTitle": "Story of <PERSON>", "storyMusaDesc": "The story of <PERSON> and his mission to Pharaoh", "storyPrincessTitle": "The Story of Princess", "storyPrincessDesc": "The story of Princess and her journey of faith", "storyAbuBakrTitle": "Abu Ba<PERSON>r <PERSON>", "storyAbuBakrDesc": "The story of the first <PERSON><PERSON><PERSON> and closest companion of the <PERSON>", "storyUmarTitle": "<PERSON><PERSON>", "storyUmarDesc": "The story of the second <PERSON><PERSON><PERSON> and his just rule", "storyMerchantTitle": "The Honest Merchant", "storyMerchantDesc": "A story about honesty and integrity in business", "storyBeggarTitle": "The Generous Beggar", "storyBeggarDesc": "A story about generosity even when having little", "storySaladinTitle": "<PERSON><PERSON><PERSON> the Liberator", "storySaladinDesc": "The story of <PERSON><PERSON><PERSON> and the liberation of Jerusalem", "storyIbrahimTitle": "Prophet <PERSON>", "storyIbrahimDesc": "The story of <PERSON> and his unwavering faith", "storyUthmanTitle": "<PERSON><PERSON><PERSON> ibn <PERSON>", "storyUthmanDesc": "The third <PERSON><PERSON><PERSON> and his contribution to the Quran", "storyFarmerTitle": "The Patient Farmer", "storyFarmerDesc": "A story about patience and trust in divine timing", "storyKhalidTitle": "<PERSON>", "storyKhalidDesc": "The story of the Sword of Allah and his military genius", "storyIsaTitle": "Prophet <PERSON>", "storyIsaDesc": "The story of Prophet <PERSON> and his miraculous birth", "messageSendFailed": "Message sending failed, please try again later", "selectOption": "Selected option {option}", "@selectOption": {"placeholders": {"option": {"type": "String"}}}, "chatSessions": "History", "noChatSessions": "No history", "messages": "messages", "clearAllSessions": "Clear All Sessions", "clearAllConfirmation": "Are you sure you want to clear all sessions? This action cannot be undone.", "clearAll": "Clear All", "delete": "Delete", "storyDetail": "Story Detail", "favorite": "Favorite", "like": "Like", "storyCharacters": "Characters", "storyDescription": "Story Description", "startStory": "Start Story", "category": "Category", "duration": "Duration", "minutes": "minutes", "newSessionNameHint": "Enter new session name", "noChatSessionsHint": "No history", "selectAvatarHint": "Select avatar", "avatarResourcesNotAdded": "Avatar resources not added yet", "avatarResourcesHint": "Please add avatar files to assets/avatars/ directory", "nicknamePersonalizationHint": "Set a personalized nickname to show your identity", "nicknameLengthError": "Nickname cannot exceed 20 characters", "languageSelectionHint": "Select your preferred app language", "webViewLoading": "Loading...", "webViewLoadFailed": "Page loading failed", "retryButton": "Retry", "backButton": "Back", "startStoryFailed": "Failed to start story: {error}", "userDefaultName": "User", "imageLoadFailed": "Image loading failed", "previousButton": "Previous", "nextButton": "Next", "noMessages": "No messages", "testPageMessageDisabled": "This is a test page, message sending is disabled", "@startStoryFailed": {"placeholders": {"error": {"type": "String"}}}, "myLikes": "My Likes", "myFavorites": "My Favorites", "likedStories": "Liked Stories", "favoriteStories": "Favorite Stories", "noLikedStories": "You haven't liked any stories yet", "noFavoriteStories": "You haven't favorited any stories yet", "startExploring": "Start Exploring", "likedOn": "Liked on", "favoritedOn": "Favorited on", "removeLike": "Remove Like", "removeFavorite": "Remove Favorite", "confirmRemoveLike": "Are you sure you want to remove like from this story?", "confirmRemoveFavorite": "Are you sure you want to remove this story from favorites?", "operationSuccess": "Operation Successful", "operationFailed": "Operation Failed", "deleteSelected": "Delete Selected", "cancelSelection": "Cancel Selection", "selectSessions": "Select Sessions", "deleteSelectedConfirmation": "Are you sure you want to delete {count} selected sessions?", "deleteSuccess": "Delete Success", "deleteFailed": "Delete Failed", "sessionsSelected": "{count} sessions selected"}