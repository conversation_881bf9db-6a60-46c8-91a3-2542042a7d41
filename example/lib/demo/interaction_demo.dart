import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import '../pages/story_detail_page.dart';
import '../pages/profile_page.dart';
import '../models/story.dart';
import '../models/story_category.dart';

/// 点赞和收藏功能演示
/// 这个演示展示了完整的用户交互功能：
/// 1. 故事详情页面的点赞和收藏操作
/// 2. 个人资料页面的交互统计网格
/// 3. 点赞列表页面和收藏列表页面
/// 4. 数据持久化和状态同步
class InteractionDemo extends StatefulWidget {
  const InteractionDemo({super.key});

  @override
  State<InteractionDemo> createState() => _InteractionDemoState();
}

class _InteractionDemoState extends State<InteractionDemo> {
  int _currentIndex = 0;
  late List<StoryCategory> _categories;
  late List<Story> _stories;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // 创建测试分类
    _categories = [
      StoryCategory(
        id: 'suspense',
        name: '悬疑',
        icon: Icons.psychology,
        color: Colors.purple,
      ),
      StoryCategory(
        id: 'scifi',
        name: '科幻',
        icon: Icons.rocket_launch,
        color: Colors.blue,
      ),
      StoryCategory(
        id: 'fantasy',
        name: '奇幻',
        icon: Icons.auto_awesome,
        color: Colors.green,
      ),
      StoryCategory(
        id: 'history',
        name: '历史',
        icon: Icons.history_edu,
        color: Colors.brown,
      ),
    ];

    // 创建测试故事
    _stories = [
      Story(
        id: 'story_1',
        title: {
          'zh': '神秘的古堡',
          'en': 'The Mysterious Castle',
          'ar': 'القلعة الغامضة',
          'hi': 'रहस्यमय महल'
        },
        backgroundSetting: {
          'zh': '在一个风雨交加的夜晚，你来到了一座古老的城堡前。城堡高耸入云，四周被浓雾笼罩，显得格外神秘。',
          'en': 'On a stormy night, you arrive at an ancient castle. The castle towers into the clouds, surrounded by thick fog.',
        },
        characterSetting: {
          'zh': '你是一位经验丰富的探险家，拥有敏锐的观察力和勇敢的心。',
          'en': 'You are an experienced explorer with keen observation skills and a brave heart.',
        },
        currentPlot: {
          'zh': '当你推开城堡的大门时，一阵冷风扑面而来...',
          'en': 'As you push open the castle door, a cold wind hits your face...',
        },
        imageUrl: 'assets/images/stories/castle.jpg',
        categoryId: 'suspense',
        popularity: 95,
      ),
      Story(
        id: 'story_2',
        title: {
          'zh': '星际穿越',
          'en': 'Interstellar Journey',
          'ar': 'رحلة بين النجوم',
          'hi': 'तारों के बीच यात्रा'
        },
        backgroundSetting: {
          'zh': '2157年，地球资源枯竭，人类必须寻找新的家园。你是一名宇航员，即将踏上前往未知星系的旅程。',
          'en': 'In 2157, Earth\'s resources are depleted, and humanity must find a new home.',
        },
        characterSetting: {
          'zh': '你是一名经验丰富的宇航员，具备出色的驾驶技能和冷静的判断力。',
          'en': 'You are an experienced astronaut with excellent piloting skills and calm judgment.',
        },
        currentPlot: {
          'zh': '飞船即将进入超光速状态，目标是距离地球50光年的开普勒-442b...',
          'en': 'The spacecraft is about to enter hyperspace, targeting Kepler-442b, 50 light-years from Earth...',
        },
        imageUrl: 'assets/images/stories/space.jpg',
        categoryId: 'scifi',
        popularity: 88,
      ),
      Story(
        id: 'story_3',
        title: {
          'zh': '魔法学院',
          'en': 'Magic Academy',
          'ar': 'أكاديمية السحر',
          'hi': 'जादू की अकादमी'
        },
        backgroundSetting: {
          'zh': '在一个充满魔法的世界里，你收到了来自著名魔法学院的入学通知书。',
          'en': 'In a world full of magic, you receive an admission letter from a famous magic academy.',
        },
        characterSetting: {
          'zh': '你是一个刚刚觉醒魔法天赋的年轻人，对魔法世界充满好奇。',
          'en': 'You are a young person who has just awakened magical talents, curious about the magical world.',
        },
        currentPlot: {
          'zh': '踏进学院大门的那一刻，你感受到了空气中浓郁的魔法气息...',
          'en': 'The moment you step through the academy gates, you feel the rich magical atmosphere in the air...',
        },
        imageUrl: 'assets/images/stories/magic.jpg',
        categoryId: 'fantasy',
        popularity: 92,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '点赞收藏功能演示',
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh'),
        Locale('en'),
        Locale('ar'),
        Locale('hi'),
      ],
      home: Scaffold(
        body: IndexedStack(
          index: _currentIndex,
          children: [
            _buildStoryList(),
            ProfilePage(),
          ],
        ),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) => setState(() => _currentIndex = index),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.book),
              label: '故事',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person),
              label: '个人',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryList() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('故事列表'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _stories.length,
        itemBuilder: (context, index) {
          final story = _stories[index];
          final category = _categories.firstWhere(
            (cat) => cat.id == story.categoryId,
            orElse: () => _categories.first,
          );
          
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: InkWell(
              onTap: () => _navigateToStoryDetail(story),
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // 故事封面占位符
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: category.color.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        category.icon,
                        size: 40,
                        color: category.color,
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // 故事信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            story.title['zh'] ?? '',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: category.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: category.color.withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              category.name,
                              style: TextStyle(
                                fontSize: 12,
                                color: category.color,
                                fontWeight: FontWeight.medium,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            story.backgroundSetting['zh'] ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    const Icon(Icons.arrow_forward_ios, size: 16),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _navigateToStoryDetail(Story story) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: _categories,
        ),
      ),
    );
  }
}

void main() {
  runApp(const InteractionDemo());
}
